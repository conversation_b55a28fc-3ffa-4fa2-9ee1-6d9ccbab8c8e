{% extends 'layout.html' %}

{% block head %}
<title>Developer Settings - LHDN e-Invoice Portal</title>
<link href="/assets/css/components/tooltip.css" rel="stylesheet">
<link href="/assets/css/pages/developer-settings.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<!-- Rich Text Editor -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
{% endblock %}

{% block content %}
<div class="container-fluid px-3 px-md-4 px-lg-5">
  <!-- Header -->
  <div class="profile-welcome-card">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <div class="welcome-icon">
          <i class="fas fa-code"></i>
        </div>
        <div class="welcome-content">
          <h4 class="mb-1">Developer Settings</h4>
          <p class="mb-0">Manage announcements, news, and portal settings</p>
        </div>
      </div>
      <div class="developer-actions">
        <button class="btn btn-primary" id="createAnnouncementBtn">
          <i class="fas fa-plus"></i> New Announcement
        </button>
      </div>
    </div>
  </div>

  <!-- Settings Navigation -->
  <div class="row">
    <div class="col-lg-3">
      <div class="card settings-nav-card">
        <div class="card-body">
          <h6 class="settings-nav-title">
            <i class="fas fa-cogs"></i>
            Developer Tools
          </h6>

          <div class="settings-nav-items">
            <a href="#announcements" class="settings-nav-item active" data-section="announcements">
              <div class="settings-nav-icon">
                <i class="fas fa-bullhorn"></i>
              </div>
              <div class="settings-nav-details">
                <h6>Announcements</h6>
                <p>Manage portal announcements</p>
              </div>
            </a>

            <a href="#news" class="settings-nav-item" data-section="news">
              <div class="settings-nav-icon">
                <i class="fas fa-newspaper"></i>
              </div>
              <div class="settings-nav-details">
                <h6>News & Updates</h6>
                <p>Post news and updates</p>
              </div>
            </a>

            <a href="#help-system" class="settings-nav-item" data-section="help-system">
              <div class="settings-nav-icon">
                <i class="fas fa-question-circle"></i>
              </div>
              <div class="settings-nav-details">
                <h6>Help System</h6>
                <p>Manage tutorials and help</p>
              </div>
            </a>

            <a href="#portal-settings" class="settings-nav-item" data-section="portal-settings">
              <div class="settings-nav-icon">
                <i class="fas fa-sliders-h"></i>
              </div>
              <div class="settings-nav-details">
                <h6>Portal Settings</h6>
                <p>Advanced portal configuration</p>
              </div>
            </a>

            <a href="#system-monitoring" class="settings-nav-item" data-section="system-monitoring">
              <div class="settings-nav-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="settings-nav-details">
                <h6>System Monitoring</h6>
                <p>Monitor system health</p>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-9">
      <!-- Announcements Section -->
      <div class="settings-section active" id="announcements">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-bullhorn me-2"></i>
              Announcements Management
            </h5>
          </div>
          <div class="card-body">
            <!-- Announcement Stats -->
            <div class="row mb-4">
              <div class="col-md-3">
                <div class="stat-card">
                  <div class="stat-icon bg-primary">
                    <i class="fas fa-bullhorn"></i>
                  </div>
                  <div class="stat-content">
                    <h4 id="totalAnnouncements">0</h4>
                    <p>Total Announcements</p>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card">
                  <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="stat-content">
                    <h4 id="activeAnnouncements">0</h4>
                    <p>Active</p>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card">
                  <div class="stat-icon bg-warning">
                    <i class="fas fa-edit"></i>
                  </div>
                  <div class="stat-content">
                    <h4 id="draftAnnouncements">0</h4>
                    <p>Drafts</p>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card">
                  <div class="stat-icon bg-info">
                    <i class="fas fa-thumbtack"></i>
                  </div>
                  <div class="stat-content">
                    <h4 id="pinnedAnnouncements">0</h4>
                    <p>Pinned</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Announcements Filter -->
            <div class="announcements-filter mb-3">
              <div class="row align-items-center">
                <div class="col-md-8">
                  <div class="filter-buttons">
                    <button class="btn btn-outline-primary btn-sm active" data-filter="all">All</button>
                    <button class="btn btn-outline-success btn-sm" data-filter="published">Published</button>
                    <button class="btn btn-outline-warning btn-sm" data-filter="draft">Drafts</button>
                    <button class="btn btn-outline-secondary btn-sm" data-filter="archived">Archived</button>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="input-group">
                    <input type="text" class="form-control form-control-sm" placeholder="Search announcements..." id="announcementSearch">
                    <button class="btn btn-outline-secondary btn-sm" type="button">
                      <i class="fas fa-search"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Announcements Table -->
            <div class="table-responsive">
              <table class="table table-hover" id="announcementsTable">
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Target</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="announcementsTableBody">
                  <!-- Announcements will be loaded here -->
                </tbody>
              </table>
            </div>

            <!-- Loading State -->
            <div id="announcementsLoading" class="text-center p-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2 text-muted">Loading announcements...</p>
            </div>

            <!-- Empty State -->
            <div id="announcementsEmpty" class="text-center p-5" style="display: none;">
              <i class="fas fa-bullhorn text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">No announcements found</h5>
              <p class="text-muted">Create your first announcement to get started.</p>
              <button class="btn btn-primary" onclick="showCreateAnnouncementModal()">
                <i class="fas fa-plus"></i> Create Announcement
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- News Section -->
      <div class="settings-section" id="news" style="display: none;">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-newspaper me-2"></i>
              News & Updates Management
            </h5>
          </div>
          <div class="card-body">
            <p class="text-muted">Manage news articles and updates for the portal.</p>
            <!-- News management content will be added here -->
          </div>
        </div>
      </div>

      <!-- Help System Section -->
      <div class="settings-section" id="help-system" style="display: none;">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-question-circle me-2"></i>
              Help System Management
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <h6>Current Help URL</h6>
                <div class="input-group mb-3">
                  <input type="url" class="form-control" value="http://pxcserver.ddns.net:3000/help#tutorials" id="helpUrl">
                  <button class="btn btn-outline-primary" type="button" onclick="updateHelpUrl()">
                    <i class="fas fa-save"></i> Update
                  </button>
                </div>
              </div>
              <div class="col-md-6">
                <h6>Quick Actions</h6>
                <div class="d-grid gap-2">
                  <button class="btn btn-outline-primary" onclick="openHelpEditor()">
                    <i class="fas fa-edit"></i> Edit Help Content
                  </button>
                  <button class="btn btn-outline-info" onclick="previewHelp()">
                    <i class="fas fa-eye"></i> Preview Help
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Portal Settings Section -->
      <div class="settings-section" id="portal-settings" style="display: none;">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-sliders-h me-2"></i>
              Advanced Portal Settings
            </h5>
          </div>
          <div class="card-body">
            <!-- API & Performance Settings -->
            <div class="setting-group">
              <h6><i class="fas fa-tachometer-alt me-2"></i>API & Performance</h6>
              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">LHDN API Rate Limit</label>
                    <input type="number" class="form-control" value="300" id="apiRateLimit">
                    <small class="form-text text-muted">Requests per minute (LHDN max: 300)</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">API Timeout</label>
                    <input type="number" class="form-control" value="30" id="apiTimeout">
                    <small class="form-text text-muted">Seconds</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Retry Attempts</label>
                    <input type="number" class="form-control" value="3" id="retryAttempts">
                    <small class="form-text text-muted">Failed request retries</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Security Settings -->
            <div class="setting-group">
              <h6><i class="fas fa-shield-alt me-2"></i>Security & Authentication</h6>
              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Session Timeout</label>
                    <input type="number" class="form-control" value="30" id="sessionTimeout">
                    <small class="form-text text-muted">Minutes</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Max Login Attempts</label>
                    <input type="number" class="form-control" value="5" id="maxLoginAttempts">
                    <small class="form-text text-muted">Before account lockout</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Lockout Duration</label>
                    <input type="number" class="form-control" value="15" id="lockoutDuration">
                    <small class="form-text text-muted">Minutes</small>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="enforceHttps" checked>
                    <label class="form-check-label" for="enforceHttps">
                      Enforce HTTPS
                    </label>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="enableTwoFactor">
                    <label class="form-check-label" for="enableTwoFactor">
                      Enable 2FA for Admins
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Data Management -->
            <div class="setting-group">
              <h6><i class="fas fa-database me-2"></i>Data Management</h6>
              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Log Retention</label>
                    <input type="number" class="form-control" value="90" id="logRetention">
                    <small class="form-text text-muted">Days</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Notification Retention</label>
                    <input type="number" class="form-control" value="30" id="notificationRetention">
                    <small class="form-text text-muted">Days</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">File Cleanup</label>
                    <select class="form-select" id="fileCleanup">
                      <option value="never">Never</option>
                      <option value="30">30 days</option>
                      <option value="90" selected>90 days</option>
                      <option value="180">180 days</option>
                      <option value="365">1 year</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                    <label class="form-check-label" for="autoBackup">
                      Enable Automatic Backups
                    </label>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="compressLogs">
                    <label class="form-check-label" for="compressLogs">
                      Compress Old Logs
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Email & Notifications -->
            <div class="setting-group">
              <h6><i class="fas fa-envelope me-2"></i>Email & Notifications</h6>
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">SMTP Server</label>
                    <input type="text" class="form-control" placeholder="smtp.example.com" id="smtpServer">
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="mb-3">
                    <label class="form-label">SMTP Port</label>
                    <input type="number" class="form-control" value="587" id="smtpPort">
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="mb-3">
                    <label class="form-label">Encryption</label>
                    <select class="form-select" id="smtpEncryption">
                      <option value="tls" selected>TLS</option>
                      <option value="ssl">SSL</option>
                      <option value="none">None</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">From Email</label>
                    <input type="email" class="form-control" placeholder="<EMAIL>" id="fromEmail">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Admin Email</label>
                    <input type="email" class="form-control" placeholder="<EMAIL>" id="adminEmail">
                  </div>
                </div>
              </div>
            </div>

            <!-- System Maintenance -->
            <div class="setting-group">
              <h6><i class="fas fa-tools me-2"></i>System Maintenance</h6>
              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Maintenance Mode</label>
                    <select class="form-select" id="maintenanceMode">
                      <option value="off" selected>Off</option>
                      <option value="scheduled">Scheduled</option>
                      <option value="immediate">Immediate</option>
                    </select>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Maintenance Start</label>
                    <input type="datetime-local" class="form-control" id="maintenanceStart">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Maintenance End</label>
                    <input type="datetime-local" class="form-control" id="maintenanceEnd">
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <label class="form-label">Maintenance Message</label>
                <textarea class="form-control" rows="2" id="maintenanceMessage" placeholder="System is under maintenance. Please try again later."></textarea>
              </div>
            </div>

            <!-- Save Settings -->
            <div class="text-end">
              <button class="btn btn-outline-secondary me-2" onclick="resetPortalSettings()">
                <i class="fas fa-undo"></i> Reset to Defaults
              </button>
              <button class="btn btn-primary" onclick="savePortalSettings()">
                <i class="fas fa-save"></i> Save Settings
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- System Monitoring Section -->
      <div class="settings-section" id="system-monitoring" style="display: none;">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-line me-2"></i>
              System Monitoring Dashboard
            </h5>
          </div>
          <div class="card-body">
            <!-- System monitoring content will be added here -->
            <div class="row">
              <div class="col-md-4">
                <div class="monitoring-card">
                  <h6>System Health</h6>
                  <div class="health-indicator good">
                    <i class="fas fa-check-circle"></i> Healthy
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="monitoring-card">
                  <h6>Database Status</h6>
                  <div class="health-indicator good">
                    <i class="fas fa-database"></i> Connected
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="monitoring-card">
                  <h6>LHDN API Status</h6>
                  <div class="health-indicator good">
                    <i class="fas fa-globe"></i> Online
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create/Edit Announcement Modal -->
<div class="modal fade" id="announcementModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="announcementModalTitle">Create Announcement</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="announcementForm">
          <input type="hidden" id="announcementId">

          <div class="row">
            <div class="col-md-8">
              <div class="mb-3">
                <label class="form-label">Title *</label>
                <input type="text" class="form-control" id="announcementTitle" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Type</label>
                <select class="form-select" id="announcementType">
                  <option value="general">General</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="feature">Feature</option>
                  <option value="security">Security</option>
                </select>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">Summary</label>
            <textarea class="form-control" rows="2" id="announcementSummary" placeholder="Brief summary for notifications"></textarea>
          </div>

          <div class="mb-3">
            <label class="form-label">Content *</label>
            <textarea id="announcementContent" class="form-control" rows="8"></textarea>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Priority</label>
                <select class="form-select" id="announcementPriority">
                  <option value="low">Low</option>
                  <option value="normal" selected>Normal</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Target Audience</label>
                <select class="form-select" id="announcementAudience">
                  <option value="all">All Users</option>
                  <option value="admin">Administrators</option>
                  <option value="users">Regular Users</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Options</label>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="announcementPinned">
                  <label class="form-check-label" for="announcementPinned">
                    Pin to top
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="announcementPopup">
                  <label class="form-check-label" for="announcementPopup">
                    Show as popup
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Publish Date</label>
                <input type="datetime-local" class="form-control" id="announcementPublishDate">
                <small class="form-text text-muted">Leave empty to publish immediately</small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Expiry Date</label>
                <input type="datetime-local" class="form-control" id="announcementExpiryDate">
                <small class="form-text text-muted">Leave empty for no expiry</small>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-outline-primary" id="saveAsDraftBtn">Save as Draft</button>
        <button type="button" class="btn btn-primary" id="publishAnnouncementBtn">Publish</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/assets/js/pages/developer-settings.js"></script>
{% endblock %}
